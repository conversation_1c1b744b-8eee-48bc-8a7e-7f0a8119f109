// Video Player Enhanced Functionality with Modern UI
class VideoPlayer {
    constructor(captionStudio) {
        this.app = captionStudio;
        this.playbackRate = 1.0;
        this.isFullscreen = false;
        this.volume = 1;
        this.isMuted = false;

        // Enhanced caption settings with modern defaults
        this.captionSettings = {
            fontSize: 24,
            fontFamily: 'Inter, sans-serif',
            color: '#ffffff',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            showBackground: true,
            positionX: 50,
            positionY: 85,
            width: 80,
            borderRadius: 8,
            padding: 12,
            textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)',
            lineHeight: 1.4
        };

        // Performance and state management
        this.state = {
            isPlaying: false,
            currentTime: 0,
            duration: 0,
            buffered: 0,
            seeking: false,
            loading: false
        };

        // Animation frame for smooth updates
        this.animationFrame = null;
        this.lastUpdateTime = 0;
        this.updateThrottle = 16; // ~60fps

        this.setupAdvancedControls();
        this.setupKeyboardShortcuts();
        this.setupVideoEventListeners();
    }

    setupVideoEventListeners() {
        if (!this.app.videoPlayer) return;

        // Enhanced video event handling
        this.app.videoPlayer.addEventListener('loadstart', () => {
            this.state.loading = true;
            this.app.updateStatus('Loading video...', 'processing');
        });

        this.app.videoPlayer.addEventListener('loadedmetadata', () => {
            this.state.duration = this.app.videoPlayer.duration;
            this.app.updateStatus('Video loaded successfully', 'success');
        });

        this.app.videoPlayer.addEventListener('canplay', () => {
            this.state.loading = false;
        });

        this.app.videoPlayer.addEventListener('timeupdate', () => {
            this.throttledUpdate(() => {
                this.state.currentTime = this.app.videoPlayer.currentTime;
                this.updateCaptionDisplay();
                this.updateProgressBar();
            });
        });

        this.app.videoPlayer.addEventListener('play', () => {
            this.state.isPlaying = true;
            this.startProgressUpdates();
        });

        this.app.videoPlayer.addEventListener('pause', () => {
            this.state.isPlaying = false;
            this.stopProgressUpdates();
        });

        this.app.videoPlayer.addEventListener('ended', () => {
            this.state.isPlaying = false;
            this.stopProgressUpdates();
            this.app.updateStatus('Video playback completed');
        });

        this.app.videoPlayer.addEventListener('error', (e) => {
            this.app.showError('Video playback error', e.message);
        });
    }

    throttledUpdate(callback) {
        const now = Date.now();
        if (now - this.lastUpdateTime >= this.updateThrottle) {
            this.lastUpdateTime = now;
            if (this.animationFrame) {
                cancelAnimationFrame(this.animationFrame);
            }
            this.animationFrame = requestAnimationFrame(callback);
        }
    }

    startProgressUpdates() {
        this.stopProgressUpdates(); // Clear any existing interval
        this.updateInterval = setInterval(() => {
            if (this.state.isPlaying && !this.state.seeking) {
                this.updateProgressBar();
            }
        }, 100); // Update every 100ms
    }

    stopProgressUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    updateProgressBar() {
        // Update any progress bars or timeline indicators
        if (this.app.timelineProgress) {
            const progress = (this.state.currentTime / this.state.duration) * 100;
            this.app.timelineProgress.style.width = `${progress}%`;
        }
    }

    updateCaptionDisplay() {
        if (!this.app.transcriptionData || !this.app.transcriptionData.words) return;

        const currentTime = this.state.currentTime;
        const currentWord = this.app.transcriptionData.words.find(word =>
            currentTime >= word.start && currentTime <= word.end
        );

        if (currentWord && this.app.captionOverlay) {
            this.renderCaption(currentWord.word);
        } else if (this.app.captionOverlay) {
            this.app.captionOverlay.textContent = '';
        }
    }

    renderCaption(text) {
        if (!this.app.captionOverlay) return;

        this.app.captionOverlay.textContent = text;

        // Use the main app's caption style settings for consistency
        if (text) {
            this.app.updateCaptionStyle();
        }
            text-shadow: ${styles.textShadow};
            line-height: ${styles.lineHeight};
            max-width: ${styles.width}%;
            text-align: center;
            word-wrap: break-word;
            z-index: 10;
            pointer-events: none;
            transition: opacity 0.2s ease-in-out;
        `;
    }

    setupAdvancedControls() {
        // Add playback speed control
        this.addPlaybackSpeedControl();
        
        // Add fullscreen toggle
        this.addFullscreenControl();
        
        // Enhanced seek bar with preview
        this.enhanceSeekBar();
        
        // Picture-in-picture support
        this.addPictureInPictureSupport();
    }

    addPlaybackSpeedControl() {
        const speedControl = document.createElement('div');
        speedControl.className = 'speed-control';
        speedControl.style.cssText = `
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: 15px;
        `;

        const speedLabel = document.createElement('span');
        speedLabel.textContent = 'Speed:';
        speedLabel.style.cssText = `
            font-size: 0.9rem;
            color: #a0a0c0;
            min-width: 45px;
        `;

        const speedSelect = document.createElement('select');
        speedSelect.className = 'form-control';
        speedSelect.style.cssText = `
            width: 80px;
            padding: 4px 8px;
            font-size: 0.8rem;
        `;

        const speeds = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];
        speeds.forEach(speed => {
            const option = document.createElement('option');
            option.value = speed;
            option.textContent = speed + 'x';
            if (speed === 1.0) option.selected = true;
            speedSelect.appendChild(option);
        });

        speedSelect.addEventListener('change', (e) => {
            this.playbackRate = parseFloat(e.target.value);
            this.app.videoPlayer.playbackRate = this.playbackRate;
            this.app.updateStatus(`Playback speed: ${this.playbackRate}x`);
        });

        speedControl.appendChild(speedLabel);
        speedControl.appendChild(speedSelect);
        this.app.videoControls.appendChild(speedControl);
    }

    addFullscreenControl() {
        const fullscreenBtn = document.createElement('button');
        fullscreenBtn.className = 'control-btn';
        fullscreenBtn.textContent = '⛶';
        fullscreenBtn.title = 'Toggle Fullscreen';
        
        fullscreenBtn.addEventListener('click', () => {
            this.toggleFullscreen();
        });

        this.app.videoControls.appendChild(fullscreenBtn);
        this.fullscreenBtn = fullscreenBtn;
    }

    enhanceSeekBar() {
        const seekBar = this.app.seekBar;
        
        // Add preview thumbnail on hover (placeholder for now)
        seekBar.addEventListener('mousemove', (e) => {
            this.showSeekPreview(e);
        });

        seekBar.addEventListener('mouseleave', () => {
            this.hideSeekPreview();
        });

        // Add chapter markers if available
        this.addChapterMarkers();
    }

    showSeekPreview(event) {
        const seekBar = this.app.seekBar;
        const rect = seekBar.getBoundingClientRect();
        const percentage = (event.clientX - rect.left) / rect.width;
        const duration = this.app.videoPlayer.duration;
        const previewTime = percentage * duration;

        let preview = document.querySelector('.seek-preview');
        if (!preview) {
            preview = document.createElement('div');
            preview.className = 'seek-preview';
            preview.style.cssText = `
                position: absolute;
                background: #1a1a2a;
                color: #e6e6f0;
                padding: 6px 10px;
                border-radius: 4px;
                font-size: 0.8rem;
                border: 1px solid #3a3a5a;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 1000;
                pointer-events: none;
                transform: translateX(-50%);
            `;
            document.body.appendChild(preview);
        }

        preview.textContent = this.app.formatTime(previewTime);
        preview.style.left = event.clientX + 'px';
        preview.style.top = (rect.top - 40) + 'px';
        preview.style.display = 'block';
    }

    hideSeekPreview() {
        const preview = document.querySelector('.seek-preview');
        if (preview) {
            preview.style.display = 'none';
        }
    }

    addChapterMarkers() {
        // Add markers for segments if available
        if (this.app.transcriptionData && this.app.transcriptionData.segments) {
            const seekBar = this.app.seekBar;
            const duration = this.app.videoPlayer.duration;
            
            this.app.transcriptionData.segments.forEach((segment, index) => {
                const marker = document.createElement('div');
                marker.className = 'chapter-marker';
                marker.style.cssText = `
                    position: absolute;
                    top: 0;
                    width: 2px;
                    height: 100%;
                    background: #4db8ff;
                    opacity: 0.6;
                    pointer-events: none;
                    z-index: 1;
                `;
                
                const position = (segment.start / duration) * 100;
                marker.style.left = position + '%';
                marker.title = `Segment ${index + 1}: ${segment.text.substring(0, 50)}...`;
                
                seekBar.parentElement.style.position = 'relative';
                seekBar.parentElement.appendChild(marker);
            });
        }
    }

    addPictureInPictureSupport() {
        if ('pictureInPictureEnabled' in document) {
            const pipBtn = document.createElement('button');
            pipBtn.className = 'control-btn';
            pipBtn.textContent = '📺';
            pipBtn.title = 'Picture-in-Picture';
            
            pipBtn.addEventListener('click', async () => {
                try {
                    if (document.pictureInPictureElement) {
                        await document.exitPictureInPicture();
                    } else {
                        await this.app.videoPlayer.requestPictureInPicture();
                    }
                } catch (error) {
                    console.error('Picture-in-Picture error:', error);
                }
            });

            this.app.videoControls.appendChild(pipBtn);
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only handle when not typing in inputs
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }

            switch (e.code) {
                case 'KeyF':
                    if (!e.ctrlKey && !e.metaKey) {
                        e.preventDefault();
                        this.toggleFullscreen();
                    }
                    break;
                case 'KeyM':
                    e.preventDefault();
                    this.app.toggleMute();
                    break;
                case 'ArrowUp':
                    if (e.shiftKey) {
                        e.preventDefault();
                        this.adjustVolume(0.1);
                    }
                    break;
                case 'ArrowDown':
                    if (e.shiftKey) {
                        e.preventDefault();
                        this.adjustVolume(-0.1);
                    }
                    break;
                case 'Comma':
                    if (e.shiftKey) {
                        e.preventDefault();
                        this.changePlaybackSpeed(-0.25);
                    }
                    break;
                case 'Period':
                    if (e.shiftKey) {
                        e.preventDefault();
                        this.changePlaybackSpeed(0.25);
                    }
                    break;
                case 'Digit0':
                    e.preventDefault();
                    this.seekToPercentage(0);
                    break;
                case 'Digit1':
                    e.preventDefault();
                    this.seekToPercentage(10);
                    break;
                case 'Digit2':
                    e.preventDefault();
                    this.seekToPercentage(20);
                    break;
                case 'Digit3':
                    e.preventDefault();
                    this.seekToPercentage(30);
                    break;
                case 'Digit4':
                    e.preventDefault();
                    this.seekToPercentage(40);
                    break;
                case 'Digit5':
                    e.preventDefault();
                    this.seekToPercentage(50);
                    break;
                case 'Digit6':
                    e.preventDefault();
                    this.seekToPercentage(60);
                    break;
                case 'Digit7':
                    e.preventDefault();
                    this.seekToPercentage(70);
                    break;
                case 'Digit8':
                    e.preventDefault();
                    this.seekToPercentage(80);
                    break;
                case 'Digit9':
                    e.preventDefault();
                    this.seekToPercentage(90);
                    break;
            }
        });
    }

    toggleFullscreen() {
        const videoContainer = this.app.videoPlayer.parentElement;
        
        if (!document.fullscreenElement) {
            videoContainer.requestFullscreen().then(() => {
                this.isFullscreen = true;
                this.fullscreenBtn.textContent = '⛶';
                videoContainer.classList.add('fullscreen');
            }).catch(err => {
                console.error('Error entering fullscreen:', err);
            });
        } else {
            document.exitFullscreen().then(() => {
                this.isFullscreen = false;
                this.fullscreenBtn.textContent = '⛶';
                videoContainer.classList.remove('fullscreen');
            });
        }
    }

    adjustVolume(delta) {
        const newVolume = Math.max(0, Math.min(1, this.app.videoPlayer.volume + delta));
        this.app.videoPlayer.volume = newVolume;
        this.app.volumeBar.value = newVolume * 100;
        this.app.updateVolumeIcon();
        this.app.updateStatus(`Volume: ${Math.round(newVolume * 100)}%`);
    }

    changePlaybackSpeed(delta) {
        const newSpeed = Math.max(0.25, Math.min(2.0, this.playbackRate + delta));
        this.playbackRate = newSpeed;
        this.app.videoPlayer.playbackRate = newSpeed;
        
        // Update speed control if it exists
        const speedSelect = document.querySelector('.speed-control select');
        if (speedSelect) {
            speedSelect.value = newSpeed;
        }
        
        this.app.updateStatus(`Playback speed: ${newSpeed}x`);
    }

    seekToPercentage(percentage) {
        const duration = this.app.videoPlayer.duration;
        if (duration) {
            const newTime = (percentage / 100) * duration;
            this.app.videoPlayer.currentTime = newTime;
            this.app.updateStatus(`Seek to ${percentage}%`);
        }
    }

    // Caption rendering with advanced styling
    renderCaption(text, settings = {}) {
        const overlay = this.app.captionOverlay;

        overlay.textContent = text;
        overlay.style.display = text ? 'block' : 'none';

        if (text) {
            // Use the main app's caption style settings for consistency
            this.app.updateCaptionStyle();
        }
    }

    applyCaptionStyling(overlay, settings) {
        overlay.style.fontFamily = settings.fontFamily;
        overlay.style.fontSize = settings.fontSize + 'px';
        overlay.style.color = settings.color;
        overlay.style.left = settings.positionX + '%';
        overlay.style.top = settings.positionY + '%';
        overlay.style.maxWidth = settings.width + '%';
        
        if (settings.showBackground) {
            overlay.style.background = settings.backgroundColor + 'CC';
            overlay.style.padding = '10px 20px';
            overlay.style.borderRadius = '8px';
        } else {
            overlay.style.background = 'transparent';
            overlay.style.padding = '0';
            overlay.style.borderRadius = '0';
        }

        // Add text shadow for better readability
        overlay.style.textShadow = '2px 2px 4px rgba(0,0,0,0.8), -1px -1px 2px rgba(0,0,0,0.8)';
        
        // Add subtle animation
        overlay.style.transition = 'opacity 0.2s ease-in-out';
    }

    // Advanced caption features
    addCaptionAnimation(type = 'fade') {
        const overlay = this.app.captionOverlay;
        
        switch (type) {
            case 'fade':
                overlay.style.animation = 'captionFadeIn 0.3s ease-in-out';
                break;
            case 'slide':
                overlay.style.animation = 'captionSlideUp 0.3s ease-out';
                break;
            case 'typewriter':
                this.typewriterEffect(overlay);
                break;
        }
    }

    typewriterEffect(element) {
        const text = element.textContent;
        element.textContent = '';
        element.style.display = 'block';
        
        let i = 0;
        const typeInterval = setInterval(() => {
            element.textContent += text.charAt(i);
            i++;
            if (i >= text.length) {
                clearInterval(typeInterval);
            }
        }, 50);
    }

    // Export caption settings
    getCaptionSettings() {
        return {
            fontFamily: this.app.fontFamily.value,
            fontSize: parseInt(this.app.fontSize.value),
            color: this.app.fontColor.value,
            backgroundColor: this.app.backgroundColor.value,
            showBackground: this.app.showBackground.checked,
            positionX: parseInt(this.app.positionX.value),
            positionY: parseInt(this.app.positionY.value),
            width: parseInt(this.app.captionWidth.value)
        };
    }

    // Load caption settings
    loadCaptionSettings(settings) {
        if (settings.fontFamily) this.app.fontFamily.value = settings.fontFamily;
        if (settings.fontSize) {
            this.app.fontSize.value = settings.fontSize;
            this.app.fontSizeValue.textContent = settings.fontSize + 'px';
        }
        if (settings.color) this.app.fontColor.value = settings.color;
        if (settings.backgroundColor) this.app.backgroundColor.value = settings.backgroundColor;
        if (typeof settings.showBackground === 'boolean') this.app.showBackground.checked = settings.showBackground;
        if (settings.positionX) {
            this.app.positionX.value = settings.positionX;
            this.app.positionXValue.textContent = settings.positionX + '%';
        }
        if (settings.positionY) {
            this.app.positionY.value = settings.positionY;
            this.app.positionYValue.textContent = settings.positionY + '%';
        }
        if (settings.width) {
            this.app.captionWidth.value = settings.width;
            this.app.captionWidthValue.textContent = settings.width + '%';
        }
        
        // Apply the loaded settings
        this.app.updateCaptionStyle();
    }
}

// Add CSS animations for captions
const style = document.createElement('style');
style.textContent = `
    @keyframes captionFadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes captionSlideUp {
        from { 
            opacity: 0; 
            transform: translateX(-50%) translateY(20px); 
        }
        to { 
            opacity: 1; 
            transform: translateX(-50%) translateY(0); 
        }
    }
    
    .fullscreen {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 9999 !important;
        background: #000 !important;
    }
    
    .fullscreen video {
        width: 100% !important;
        height: 100% !important;
        object-fit: contain !important;
    }
    
    .chapter-marker:hover {
        opacity: 1 !important;
        width: 4px !important;
    }
`;
document.head.appendChild(style);

// Initialize video player when main app is ready
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (window.captionStudio) {
            window.videoPlayer = new VideoPlayer(window.captionStudio);
        }
    }, 100);
});

